<?php

namespace App\Filament\Resources\Admin;

use App\Filament\Resources\Admin\OrcResource\Pages;
use App\Filament\Resources\Admin\OrcResource\RelationManagers;
use App\Models\Orc;
use App\Models\Korpus;
use App\Services\UserInfoService;
use App\Services\DoorNumberingService;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Forms\Get;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Notifications\Notification;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Validation\Rules\Unique;

class OrcResource extends Resource
{
    protected static ?string $model = Orc::class;

    protected static ?string $navigationIcon = 'heroicon-o-arrow-right-on-rectangle';
    protected static ?string $navigationLabel = 'Орцууд';
    protected static ?string $pluralModelLabel = 'Орцууд';
    protected static ?string $modelLabel = 'орц';
    protected static ?string $slug = 'orcs';

    // Hide from navigation - only accessible through Korpus hierarchy
    protected static bool $shouldRegisterNavigation = false;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make()
                    ->schema([
                        Forms\Components\Placeholder::make('korpus_info')
                            ->label('Блок')
                            ->content(function (?Orc $record) {
                                if ($record && $record->korpus) {
                                    return "{$record->korpus->bair->name} - {$record->korpus->name}";
                                }

                                $korpusId = request()->get('korpus_id');
                                if ($korpusId) {
                                    $korpus = \App\Models\Korpus::with('bair')->find($korpusId);
                                    return $korpus ? "{$korpus->bair->name} - {$korpus->name}" : 'Тодорхойгүй';
                                }

                                return 'Тодорхойгүй';
                            }),

                        Forms\Components\Hidden::make(Orc::KORPUS_ID)
                            ->default(function () {
                                return request()->get('korpus_id');
                            }),

                        Forms\Components\TextInput::make(Orc::NUMBER)
                            ->label('Орцны дугаар')
                            ->unique(
                                ignoreRecord: true,
                                modifyRuleUsing: function (Unique $rule, callable $get) {
                                    $korpusId = $get('korpus_id');
                                    return $rule->where('korpus_id', $korpusId);
                                }
                            )
                            ->numeric()
                            ->required(),
                    ])
                    ->columns(2)
                    ->columnSpan(['lg' => fn (?Orc $record) => $record === null ? 3 : 2]),

                Forms\Components\Section::make()
                    ->schema([
                        Forms\Components\Placeholder::make('code')
                            ->label('CV Security код')
                            ->content(fn (Orc $record): ?string => $record->code ?? 'Тодорхойгүй'),
                    ])
                    ->columnSpan(['lg' => 1])
                    ->hidden(fn (?Orc $record) => $record === null),

                // Auto-generation section
                Forms\Components\Section::make('Автомат үүсгэх')
                    ->schema([
                        Forms\Components\Toggle::make(Orc::AUTO_GENERATE)
                            ->label('Автомат үүсгэх')
                            ->reactive()
                            ->afterStateUpdated(function ($state, callable $get, callable $set) {
                                if (!$state) {
                                    // Clear auto-generation fields when disabled
                                    $set(Orc::NUMBERING_TYPE, null);
                                    $set(Orc::NUMBER_OF_FLOORS, null);
                                    $set(Orc::DOORS_PER_FLOOR, null);
                                    $set(Orc::DIGIT_MULTIPLIER, null);
                                }
                            }),

                        Forms\Components\Select::make(Orc::NUMBERING_TYPE)
                            ->label('Дугаарлалтын төрөл')
                            ->options([
                                2 => 'Орц бүрээр (Type 2)',
                                3 => 'Давхар бүрээр (Type 3)',
                            ])
                            ->visible(fn (Get $get) => $get(Orc::AUTO_GENERATE))
                            ->reactive()
                            ->required(fn (Get $get) => $get(Orc::AUTO_GENERATE)),

                        Forms\Components\TextInput::make(Orc::NUMBER_OF_FLOORS)
                            ->label('Давхарын тоо')
                            ->numeric()
                            ->minValue(1)
                            ->visible(fn (Get $get) => $get(Orc::AUTO_GENERATE))
                            ->required(fn (Get $get) => $get(Orc::AUTO_GENERATE)),

                        Forms\Components\TextInput::make(Orc::DOORS_PER_FLOOR)
                            ->label('Давхар тутмын тоотын тоо')
                            ->numeric()
                            ->minValue(1)
                            ->visible(fn (Get $get) => $get(Orc::AUTO_GENERATE))
                            ->required(fn (Get $get) => $get(Orc::AUTO_GENERATE)),

                        Forms\Components\Select::make(Orc::DIGIT_MULTIPLIER)
                            ->label('Цифрийн үржүүлэгч')
                            ->options([
                                10 => '10 (2 оронтой: 11, 12, 13...)',
                                100 => '100 (3 оронтой: 101, 102, 103...)',
                                1000 => '1000 (4 оронтой: 1001, 1002, 1003...)',
                            ])
                            ->visible(fn (Get $get) => $get(Orc::AUTO_GENERATE) && $get(Orc::NUMBERING_TYPE) == 3)
                            ->required(fn (Get $get) => $get(Orc::AUTO_GENERATE) && $get(Orc::NUMBERING_TYPE) == 3),

                        Forms\Components\Actions::make([
                            Forms\Components\Actions\Action::make('generate')
                                ->label('Давхар болон тоот үүсгэх')
                                ->color('success')
                                ->icon('heroicon-o-plus-circle')
                                ->visible(fn (Get $get) => $get(Orc::AUTO_GENERATE))
                                ->requiresConfirmation()
                                ->modalHeading('Давхар болон тоот үүсгэх')
                                ->modalDescription('Энэ үйлдэл нь одоо байгаа бүх давхар болон тоотуудыг устгаад шинээр үүсгэнэ. Та итгэлтэй байна уу?')
                                ->modalSubmitActionLabel('Тийм, үүсгэх')
                                ->action(function (Orc $record, array $data) {
                                    try {
                                        // Validate form data before proceeding
                                        if (!($data[Orc::AUTO_GENERATE] ?? false)) {
                                            Notification::make()
                                                ->title('Алдаа гарлаа')
                                                ->body('Автомат үүсгэх сонголтыг идэвхжүүлнэ үү.')
                                                ->danger()
                                                ->send();
                                            return;
                                        }

                                        $doorNumberingService = app(DoorNumberingService::class);

                                        // Update the record with form data first
                                        $record->update([
                                            Orc::AUTO_GENERATE => $data[Orc::AUTO_GENERATE],
                                            Orc::NUMBERING_TYPE => $data[Orc::NUMBERING_TYPE],
                                            Orc::NUMBER_OF_FLOORS => $data[Orc::NUMBER_OF_FLOORS],
                                            Orc::DOORS_PER_FLOOR => $data[Orc::DOORS_PER_FLOOR],
                                            Orc::DIGIT_MULTIPLIER => $data[Orc::DIGIT_MULTIPLIER] ?? null,
                                        ]);

                                        $doorNumberingService->generateOrcHierarchy($record);

                                        Notification::make()
                                            ->title('Амжилттай үүсгэлээ')
                                            ->body("Орц {$record->number}-д {$record->number_of_floors} давхар, давхар тутамд {$record->doors_per_floor} тоот үүсгэлээ.")
                                            ->success()
                                            ->send();
                                    } catch (\Exception $e) {
                                        Notification::make()
                                            ->title('Алдаа гарлаа')
                                            ->body($e->getMessage())
                                            ->danger()
                                            ->send();
                                    }
                                }),
                        ])
                    ])
                    ->columnSpan(['lg' => 3])
                    ->collapsible()
                    ->collapsed(),
            ])
            ->columns(3);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->heading('Орцууд')
            ->columns([
                Tables\Columns\TextColumn::make('')->label('№')->rowIndex()->sortable(),
                Tables\Columns\TextColumn::make(Orc::NUMBER)->label('Орцны дугаар')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('korpus.name')->label('Блок')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('korpus.bair.name')->label('Байр')->sortable()->searchable(),
                Tables\Columns\TextColumn::make(Orc::CODE)->label('CV Security код')->sortable(),
                Tables\Columns\TextColumn::make('davhars_count')->counts('davhars')->label('Давхарын тоо'),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->emptyStateActions([
                Tables\Actions\CreateAction::make(),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            RelationManagers\DavharsRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListOrcs::route('/'),
            'create' => Pages\CreateOrc::route('/create'),
            'edit' => Pages\EditOrc::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        $service = resolve(UserInfoService::class);
        $sukh = $service->getAUSukh();
        return parent::getEloquentQuery()->whereHas('korpus.bair.sukh', function (Builder $query) use($sukh) {
            $query->where('id', $sukh->id);
        });
    }
}
