<?php

namespace App\Services;

use App\Models\Korpus;
use App\Models\Orc;
use App\Models\Davhar;
use App\Models\Toot;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Exception;

class DoorNumberingService
{
    /**
     * Generate Davhars and Toots for an entire Korpus
     *
     * @param Korpus $korpus
     * @return void
     * @throws Exception
     */
    public function generateKorpusHierarchy(Korpus $korpus): void
    {
        // Basic validation - detailed validation is done in the UI layer
        if (!$korpus->numbering_type || !in_array($korpus->numbering_type, [1, 2, 3])) {
            throw new Exception('Invalid numbering type. Types 1 (Korpus-wise), 2 (Orc-wise), and 3 (Floor-wise) are supported for Korpus-level generation');
        }

        // Validate prerequisites - Orcs must be created before auto-generation
        if ($korpus->orcs()->count() === 0) {
            throw new Exception('Orcs must be created before enabling auto-generation');
        }

        DB::transaction(function () use ($korpus) {
            // Delete existing Davhars and Toots for all Orcs in this Korpus
            foreach ($korpus->orcs as $orc) {
                Toot::where('orc_id', $orc->id)->delete();
                $orc->davhars()->delete();
            }

            // Generate based on numbering type
            switch ($korpus->numbering_type) {
                case 1: // Korpus-wise
                    $this->generateKorpusWise($korpus);
                    break;
                case 2: // Orc-wise
                    $this->generateOrcWiseForKorpus($korpus);
                    break;
                case 3: // Floor-wise
                    $this->generateFloorWiseForKorpus($korpus);
                    break;
            }

            // Set auto_generate to false after generation
            $korpus->update(['auto_generate' => false]);
        });

        Log::info('Korpus hierarchy generated successfully', [
            'korpus_id' => $korpus->id,
            'numbering_type' => $korpus->numbering_type,
            'orcs_count' => $korpus->orcs->count(),
            'floors' => $korpus->number_of_floors,
            'doors_per_floor' => $korpus->doors_per_floor
        ]);
    }

    /**
     * Generate Davhars and Toots for a specific Orc
     *
     * @param Orc $orc
     * @return void
     * @throws Exception
     */
    public function generateOrcHierarchy(Orc $orc): void
    {
        // Basic validation - detailed validation is done in the UI layer
        if (!$orc->numbering_type || !in_array($orc->numbering_type, [2, 3])) {
            throw new Exception('Invalid numbering type. Only Type 2 (Orc-wise) and Type 3 (Floor-wise) are supported for Orc-level generation');
        }

        DB::transaction(function () use ($orc) {
            // Delete existing Davhars and Toots for this Orc
            Toot::where('orc_id', $orc->id)->delete();
            $orc->davhars()->delete();

            // Generate based on numbering type
            switch ($orc->numbering_type) {
                case 2: // Orc-wise
                    $this->generateOrcWiseForSingleOrc($orc);
                    break;
                case 3: // Floor-wise
                    $this->generateFloorWiseForSingleOrc($orc);
                    break;
            }

            // Set auto_generate to false after generation
            $orc->update(['auto_generate' => false]);
        });

        Log::info('Orc hierarchy generated successfully', [
            'orc_id' => $orc->id,
            'numbering_type' => $orc->numbering_type,
            'floors' => $orc->number_of_floors,
            'doors_per_floor' => $orc->doors_per_floor
        ]);
    }

    /**
     * Generate Toots for a specific Davhar
     *
     * @param Davhar $davhar
     * @return void
     * @throws Exception
     */
    public function generateDavharToots(Davhar $davhar): void
    {
        // Basic validation - detailed validation is done in the UI layer
        if (!$davhar->numbering_type || !in_array($davhar->numbering_type, [2, 3])) {
            throw new Exception('Invalid numbering type. Only Type 2 (Orc-wise) and Type 3 (Floor-wise) are supported for Davhar-level generation');
        }

        DB::transaction(function () use ($davhar) {
            // Delete existing Toots for this Davhar
            $davhar->toots()->delete();

            // Generate based on numbering type
            switch ($davhar->numbering_type) {
                case 2: // Orc-wise (sequential from 1)
                    $this->generateOrcWiseForDavhar($davhar);
                    break;
                case 3: // Floor-wise (floor-based numbering)
                    $this->generateFloorWiseForDavhar($davhar);
                    break;
            }

            // Set auto_generate to false after generation
            $davhar->update(['auto_generate' => false]);
        });

        Log::info('Davhar toots generated successfully', [
            'davhar_id' => $davhar->id,
            'numbering_type' => $davhar->numbering_type,
            'doors_per_floor' => $davhar->doors_per_floor
        ]);
    }

    /**
     * Generate Orc-wise numbering for a single Orc
     *
     * @param Orc $orc
     * @return void
     */
    private function generateOrcWiseForSingleOrc(Orc $orc): void
    {
        $doorNumber = 1;

        for ($floorIndex = 1; $floorIndex <= $orc->number_of_floors; $floorIndex++) {
            // Create Davhar
            $davhar = Davhar::create([
                'orc_id' => $orc->id,
                'number' => (string)$floorIndex,
                'order' => $floorIndex,
            ]);

            // Create Toots for this Davhar
            for ($doorIndex = 1; $doorIndex <= $orc->doors_per_floor; $doorIndex++) {
                Toot::create([
                    'number' => $doorNumber,
                    'korpus_id' => $orc->korpus_id,
                    'orc_id' => $orc->id,
                    'davhar_id' => $davhar->id,
                ]);
                $doorNumber++;
            }
        }
    }

    /**
     * Generate Floor-wise numbering for a single Orc
     *
     * @param Orc $orc
     * @return void
     */
    private function generateFloorWiseForSingleOrc(Orc $orc): void
    {
        for ($floorIndex = 1; $floorIndex <= $orc->number_of_floors; $floorIndex++) {
            // Create Davhar
            $davhar = Davhar::create([
                'orc_id' => $orc->id,
                'number' => (string)$floorIndex,
                'order' => $floorIndex,
            ]);

            // Create Toots for this Davhar with floor-based numbering
            for ($doorIndex = 1; $doorIndex <= $orc->doors_per_floor; $doorIndex++) {
                $doorNumber = ($floorIndex * $orc->digit_multiplier) + $doorIndex;

                Toot::create([
                    'number' => $doorNumber,
                    'korpus_id' => $orc->korpus_id,
                    'orc_id' => $orc->id,
                    'davhar_id' => $davhar->id,
                ]);
            }
        }
    }

    /**
     * Generate Orc-wise numbering for a single Davhar
     *
     * @param Davhar $davhar
     * @return void
     */
    private function generateOrcWiseForDavhar(Davhar $davhar): void
    {
        for ($doorIndex = 1; $doorIndex <= $davhar->doors_per_floor; $doorIndex++) {
            Toot::create([
                'number' => $doorIndex,
                'korpus_id' => $davhar->orc->korpus_id,
                'orc_id' => $davhar->orc_id,
                'davhar_id' => $davhar->id,
            ]);
        }
    }

    /**
     * Generate Floor-wise numbering for a single Davhar
     *
     * @param Davhar $davhar
     * @return void
     */
    private function generateFloorWiseForDavhar(Davhar $davhar): void
    {
        $floorNumber = $davhar->floor_number;

        for ($doorIndex = 1; $doorIndex <= $davhar->doors_per_floor; $doorIndex++) {
            $doorNumber = ($floorNumber * $davhar->digit_multiplier) + $doorIndex;

            Toot::create([
                'number' => $doorNumber,
                'korpus_id' => $davhar->orc->korpus_id,
                'orc_id' => $davhar->orc_id,
                'davhar_id' => $davhar->id,
            ]);
        }
    }

    /**
     * Generate Type 1 (Korpus-wise) numbering for entire Korpus
     *
     * @param Korpus $korpus
     * @return void
     */
    private function generateKorpusWise(Korpus $korpus): void
    {
        $doorNumber = 1;
        $orcs = $korpus->orcs()->orderBy('number', 'asc')->get();

        foreach ($orcs as $orc) {
            for ($floorIndex = 1; $floorIndex <= $korpus->number_of_floors; $floorIndex++) {
                // Create Davhar
                $davhar = Davhar::create([
                    'orc_id' => $orc->id,
                    'number' => (string)$floorIndex,
                    'order' => $floorIndex,
                ]);

                // Create Toots for this Davhar
                for ($doorIndex = 1; $doorIndex <= $korpus->doors_per_floor; $doorIndex++) {
                    Toot::create([
                        'number' => $doorNumber,
                        'korpus_id' => $korpus->id,
                        'orc_id' => $orc->id,
                        'davhar_id' => $davhar->id,
                    ]);
                    $doorNumber++;
                }
            }
        }
    }

    /**
     * Generate Type 2 (Orc-wise) numbering for entire Korpus
     *
     * @param Korpus $korpus
     * @return void
     */
    private function generateOrcWiseForKorpus(Korpus $korpus): void
    {
        $orcs = $korpus->orcs()->orderBy('number', 'asc')->get();

        foreach ($orcs as $orc) {
            $doorNumber = 1;

            for ($floorIndex = 1; $floorIndex <= $korpus->number_of_floors; $floorIndex++) {
                // Create Davhar
                $davhar = Davhar::create([
                    'orc_id' => $orc->id,
                    'number' => (string)$floorIndex,
                    'order' => $floorIndex,
                ]);

                // Create Toots for this Davhar
                for ($doorIndex = 1; $doorIndex <= $korpus->doors_per_floor; $doorIndex++) {
                    Toot::create([
                        'number' => $doorNumber,
                        'korpus_id' => $korpus->id,
                        'orc_id' => $orc->id,
                        'davhar_id' => $davhar->id,
                    ]);
                    $doorNumber++;
                }
            }
        }
    }

    /**
     * Generate Type 3 (Floor-wise) numbering for entire Korpus
     *
     * @param Korpus $korpus
     * @return void
     */
    private function generateFloorWiseForKorpus(Korpus $korpus): void
    {
        $orcs = $korpus->orcs()->orderBy('number', 'asc')->get();

        foreach ($orcs as $orc) {
            for ($floorIndex = 1; $floorIndex <= $korpus->number_of_floors; $floorIndex++) {
                // Create Davhar
                $davhar = Davhar::create([
                    'orc_id' => $orc->id,
                    'number' => (string)$floorIndex,
                    'order' => $floorIndex,
                ]);

                // Create Toots for this Davhar with floor-based numbering
                for ($doorIndex = 1; $doorIndex <= $korpus->doors_per_floor; $doorIndex++) {
                    $doorNumber = ($floorIndex * $korpus->digit_multiplier) + $doorIndex;

                    Toot::create([
                        'number' => $doorNumber,
                        'korpus_id' => $korpus->id,
                        'orc_id' => $orc->id,
                        'davhar_id' => $davhar->id,
                    ]);
                }
            }
        }
    }
}
