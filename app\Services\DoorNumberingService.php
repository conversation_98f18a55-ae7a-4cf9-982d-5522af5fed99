<?php

namespace App\Services;

use App\Models\Orc;
use App\Models\Davhar;
use App\Models\Toot;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Exception;

class DoorNumberingService
{
    /**
     * Generate Davhars and Toots for a specific Orc
     *
     * @param Orc $orc
     * @return void
     * @throws Exception
     */
    public function generateOrcHierarchy(Orc $orc): void
    {
        // Validate input
        if (!$orc->auto_generate) {
            throw new Exception('Auto-generation is not enabled for this Orc');
        }

        if (!$orc->numbering_type || !in_array($orc->numbering_type, [2, 3])) {
            throw new Exception('Invalid numbering type. Only Type 2 (Orc-wise) and Type 3 (Floor-wise) are supported for Orc-level generation');
        }

        if (!$orc->number_of_floors || $orc->number_of_floors <= 0) {
            throw new Exception('Number of floors must be greater than 0');
        }

        if (!$orc->doors_per_floor || $orc->doors_per_floor <= 0) {
            throw new Exception('Doors per floor must be greater than 0');
        }

        if ($orc->numbering_type === 3 && (!$orc->digit_multiplier || !in_array($orc->digit_multiplier, [10, 100, 1000]))) {
            throw new Exception('Digit multiplier must be 10, 100, or 1000 for Type 3 numbering');
        }

        DB::transaction(function () use ($orc) {
            // Delete existing Davhars and Toots for this Orc
            Toot::where('orc_id', $orc->id)->delete();
            $orc->davhars()->delete();

            // Generate based on numbering type
            switch ($orc->numbering_type) {
                case 2: // Orc-wise
                    $this->generateOrcWiseForSingleOrc($orc);
                    break;
                case 3: // Floor-wise
                    $this->generateFloorWiseForSingleOrc($orc);
                    break;
            }

            // Set auto_generate to false after generation
            $orc->update(['auto_generate' => false]);
        });

        Log::info('Orc hierarchy generated successfully', [
            'orc_id' => $orc->id,
            'numbering_type' => $orc->numbering_type,
            'floors' => $orc->number_of_floors,
            'doors_per_floor' => $orc->doors_per_floor
        ]);
    }

    /**
     * Generate Toots for a specific Davhar
     *
     * @param Davhar $davhar
     * @return void
     * @throws Exception
     */
    public function generateDavharToots(Davhar $davhar): void
    {
        // Validate input
        if (!$davhar->auto_generate) {
            throw new Exception('Auto-generation is not enabled for this Davhar');
        }

        if (!$davhar->numbering_type || !in_array($davhar->numbering_type, [2, 3])) {
            throw new Exception('Invalid numbering type. Only Type 2 (Orc-wise) and Type 3 (Floor-wise) are supported for Davhar-level generation');
        }

        if (!$davhar->doors_per_floor || $davhar->doors_per_floor <= 0) {
            throw new Exception('Doors per floor must be greater than 0');
        }

        if ($davhar->numbering_type === 3 && (!$davhar->digit_multiplier || !in_array($davhar->digit_multiplier, [10, 100, 1000]))) {
            throw new Exception('Digit multiplier must be 10, 100, or 1000 for Type 3 numbering');
        }

        DB::transaction(function () use ($davhar) {
            // Delete existing Toots for this Davhar
            $davhar->toots()->delete();

            // Generate based on numbering type
            switch ($davhar->numbering_type) {
                case 2: // Orc-wise (sequential from 1)
                    $this->generateOrcWiseForDavhar($davhar);
                    break;
                case 3: // Floor-wise (floor-based numbering)
                    $this->generateFloorWiseForDavhar($davhar);
                    break;
            }

            // Set auto_generate to false after generation
            $davhar->update(['auto_generate' => false]);
        });

        Log::info('Davhar toots generated successfully', [
            'davhar_id' => $davhar->id,
            'numbering_type' => $davhar->numbering_type,
            'doors_per_floor' => $davhar->doors_per_floor
        ]);
    }

    /**
     * Generate Orc-wise numbering for a single Orc
     *
     * @param Orc $orc
     * @return void
     */
    private function generateOrcWiseForSingleOrc(Orc $orc): void
    {
        $doorNumber = 1;

        for ($floorIndex = 1; $floorIndex <= $orc->number_of_floors; $floorIndex++) {
            // Create Davhar
            $davhar = Davhar::create([
                'orc_id' => $orc->id,
                'number' => (string)$floorIndex,
                'order' => $floorIndex,
            ]);

            // Create Toots for this Davhar
            for ($doorIndex = 1; $doorIndex <= $orc->doors_per_floor; $doorIndex++) {
                Toot::create([
                    'number' => $doorNumber,
                    'korpus_id' => $orc->korpus_id,
                    'orc_id' => $orc->id,
                    'davhar_id' => $davhar->id,
                ]);
                $doorNumber++;
            }
        }
    }

    /**
     * Generate Floor-wise numbering for a single Orc
     *
     * @param Orc $orc
     * @return void
     */
    private function generateFloorWiseForSingleOrc(Orc $orc): void
    {
        for ($floorIndex = 1; $floorIndex <= $orc->number_of_floors; $floorIndex++) {
            // Create Davhar
            $davhar = Davhar::create([
                'orc_id' => $orc->id,
                'number' => (string)$floorIndex,
                'order' => $floorIndex,
            ]);

            // Create Toots for this Davhar with floor-based numbering
            for ($doorIndex = 1; $doorIndex <= $orc->doors_per_floor; $doorIndex++) {
                $doorNumber = ($floorIndex * $orc->digit_multiplier) + $doorIndex;

                Toot::create([
                    'number' => $doorNumber,
                    'korpus_id' => $orc->korpus_id,
                    'orc_id' => $orc->id,
                    'davhar_id' => $davhar->id,
                ]);
            }
        }
    }

    /**
     * Generate Orc-wise numbering for a single Davhar
     *
     * @param Davhar $davhar
     * @return void
     */
    private function generateOrcWiseForDavhar(Davhar $davhar): void
    {
        for ($doorIndex = 1; $doorIndex <= $davhar->doors_per_floor; $doorIndex++) {
            Toot::create([
                'number' => $doorIndex,
                'korpus_id' => $davhar->orc->korpus_id,
                'orc_id' => $davhar->orc_id,
                'davhar_id' => $davhar->id,
            ]);
        }
    }

    /**
     * Generate Floor-wise numbering for a single Davhar
     *
     * @param Davhar $davhar
     * @return void
     */
    private function generateFloorWiseForDavhar(Davhar $davhar): void
    {
        $floorNumber = $davhar->floor_number;

        for ($doorIndex = 1; $doorIndex <= $davhar->doors_per_floor; $doorIndex++) {
            $doorNumber = ($floorNumber * $davhar->digit_multiplier) + $doorIndex;

            Toot::create([
                'number' => $doorNumber,
                'korpus_id' => $davhar->orc->korpus_id,
                'orc_id' => $davhar->orc_id,
                'davhar_id' => $davhar->id,
            ]);
        }
    }
}
