<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\Korpus
 *
 * @property int $id
 * @property int $bair_id
 * @property string $name
 * @property int $order
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at

 * @property-read \App\Models\Bair $bair
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Orc> $orcs
 * @property-read int|null $orcs_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Toot> $toots
 * @property-read int|null $toots_count
 * @method static \Illuminate\Database\Eloquent\Builder|Korpus newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Korpus newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Korpus query()
 * @method static \Illuminate\Database\Eloquent\Builder|Korpus whereBairId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Korpus whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Korpus whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Korpus whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Korpus whereOrder($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Korpus whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class Korpus extends Model
{
    use HasFactory;

    const ID      = 'id';
    const BAIR_ID = 'bair_id';
    const NAME    = 'name';
    const ORDER   = 'order';
    const CODE    = 'code';

    // Auto-generation constants
    const AUTO_GENERATE       = 'auto_generate';
    const NUMBERING_TYPE      = 'numbering_type';
    const NUMBER_OF_FLOORS    = 'number_of_floors';
    const DOORS_PER_FLOOR     = 'doors_per_floor';
    const DIGIT_MULTIPLIER    = 'digit_multiplier';

    const RELATION_BAIR = 'bair';
    const RELATION_ORCS = 'orcs';

    protected $fillable = [
        self::BAIR_ID,
        self::NAME,
        self::ORDER,
        self::CODE,
        self::AUTO_GENERATE,
        self::NUMBERING_TYPE,
        self::NUMBER_OF_FLOORS,
        self::DOORS_PER_FLOOR,
        self::DIGIT_MULTIPLIER
    ];

    public function bair()
    {
        return $this->belongsTo(Bair::class);
    }

    public function orcs()
    {
        return $this->hasMany(Orc::class);
    }

    public function toots()
    {
        return $this->hasMany(Toot::class)->orderBy('number', 'asc');
    }

    /**
     * Get total doors for this Korpus based on auto-generation settings
     */
    public function getTotalDoorsAttribute(): int
    {
        if ($this->doors_per_floor && $this->number_of_floors) {
            return $this->orcs->count() * $this->number_of_floors * $this->doors_per_floor;
        }

        return $this->orcs->sum(function ($orc) {
            return $orc->davhars->sum(function ($davhar) {
                return $davhar->toots->count();
            });
        });
    }
}
