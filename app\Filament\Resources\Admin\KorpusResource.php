<?php

namespace App\Filament\Resources\Admin;

use App\Filament\Resources\Admin\KorpusResource\Pages;
use App\Filament\Resources\Admin\KorpusResource\RelationManagers;
use App\Models\Korpus;
use App\Models\Bair;
use App\Services\UserInfoService;
use App\Services\DoorNumberingService;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Forms\Get;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Notifications\Notification;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Validation\Rules\Unique;

class KorpusResource extends Resource
{
    protected static ?string $model = Korpus::class;

    protected static ?string $navigationIcon = 'heroicon-o-building-office';
    protected static ?string $navigationLabel = 'Блокууд';
    protected static ?string $pluralModelLabel = 'Блокууд';
    protected static ?string $modelLabel = 'блок';
    protected static ?string $slug = 'korpuses';

    // Hide from navigation - only accessible through Bair hierarchy
    protected static bool $shouldRegisterNavigation = false;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make()
                    ->schema([
                        Forms\Components\Placeholder::make('bair_info')
                            ->label('Байр')
                            ->content(function (?Korpus $record) {
                                if ($record && $record->bair) {
                                    return $record->bair->name;
                                }

                                $bairId = request()->get('bair_id');
                                if ($bairId) {
                                    $bair = \App\Models\Bair::find($bairId);
                                    return $bair ? $bair->name : 'Тодорхойгүй';
                                }

                                return 'Тодорхойгүй';
                            }),

                        Forms\Components\Hidden::make(Korpus::BAIR_ID)
                            ->default(function () {
                                return request()->get('bair_id');
                            }),

                        Forms\Components\TextInput::make(Korpus::NAME)
                            ->label('Нэр')
                            ->unique(
                                ignoreRecord: true,
                                modifyRuleUsing: function (Unique $rule, callable $get) {
                                    $bairId = $get('bair_id');
                                    return $rule->where('bair_id', $bairId);
                                }
                            )
                            ->required(),
                    ])
                    ->columns(2)
                    ->columnSpan(['lg' => fn (?Korpus $record) => $record === null ? 3 : 2]),

                Forms\Components\Section::make()
                    ->schema([
                        Forms\Components\Placeholder::make('code')
                            ->label('CV Security код')
                            ->content(fn (Korpus $record): ?string => $record->code ?? 'Тодорхойгүй'),
                    ])
                    ->columnSpan(['lg' => 1])
                    ->hidden(fn (?Korpus $record) => $record === null),

                // Auto-generation section
                Forms\Components\Section::make('Автомат үүсгэх')
                    ->schema([
                        Forms\Components\Toggle::make(Korpus::AUTO_GENERATE)
                            ->label('Автомат үүсгэх')
                            ->reactive()
                            ->afterStateUpdated(function ($state, callable $set) {
                                if (!$state) {
                                    // Clear auto-generation fields when disabled
                                    $set(Korpus::NUMBERING_TYPE, null);
                                    $set(Korpus::NUMBER_OF_FLOORS, null);
                                    $set(Korpus::DOORS_PER_FLOOR, null);
                                    $set(Korpus::DIGIT_MULTIPLIER, null);
                                }
                            }),

                        Forms\Components\Select::make(Korpus::NUMBERING_TYPE)
                            ->label('Дугаарлалтын төрөл')
                            ->options([
                                1 => 'Блок бүрээр (Type 1)',
                                2 => 'Орц бүрээр (Type 2)',
                                3 => 'Давхар бүрээр (Type 3)',
                            ])
                            ->visible(fn (Get $get) => $get(Korpus::AUTO_GENERATE))
                            ->reactive()
                            ->required(fn (Get $get) => $get(Korpus::AUTO_GENERATE)),

                        Forms\Components\TextInput::make(Korpus::NUMBER_OF_FLOORS)
                            ->label('Орц тутмын давхарын тоо')
                            ->numeric()
                            ->minValue(1)
                            ->visible(fn (Get $get) => $get(Korpus::AUTO_GENERATE))
                            ->required(fn (Get $get) => $get(Korpus::AUTO_GENERATE)),

                        Forms\Components\TextInput::make(Korpus::DOORS_PER_FLOOR)
                            ->label('Давхар тутмын тоотын тоо')
                            ->numeric()
                            ->minValue(1)
                            ->visible(fn (Get $get) => $get(Korpus::AUTO_GENERATE))
                            ->required(fn (Get $get) => $get(Korpus::AUTO_GENERATE)),

                        Forms\Components\Select::make(Korpus::DIGIT_MULTIPLIER)
                            ->label('Цифрийн үржүүлэгч')
                            ->options([
                                10 => '10 (2 оронтой: 11, 12, 13...)',
                                100 => '100 (3 оронтой: 101, 102, 103...)',
                                1000 => '1000 (4 оронтой: 1001, 1002, 1003...)',
                            ])
                            ->visible(fn (Get $get) => $get(Korpus::AUTO_GENERATE) && $get(Korpus::NUMBERING_TYPE) == 3)
                            ->required(fn (Get $get) => $get(Korpus::AUTO_GENERATE) && $get(Korpus::NUMBERING_TYPE) == 3),

                        Forms\Components\Actions::make([
                            Forms\Components\Actions\Action::make('generate')
                                ->label('Бүх давхар болон тоот үүсгэх')
                                ->color('success')
                                ->icon('heroicon-o-plus-circle')
                                ->visible(fn (Get $get) => $get(Korpus::AUTO_GENERATE))
                                ->requiresConfirmation()
                                ->modalHeading('Бүх давхар болон тоот үүсгэх')
                                ->modalDescription('Энэ үйлдэл нь энэ блокийн бүх орцны одоо байгаа давхар болон тоотуудыг устгаад шинээр үүсгэнэ. Та итгэлтэй байна уу?')
                                ->modalSubmitActionLabel('Тийм, үүсгэх')
                                ->action(function (Korpus $record, array $data) {
                                    try {
                                        // Check if Orcs exist
                                        if ($record->orcs()->count() === 0) {
                                            Notification::make()
                                                ->title('Алдаа гарлаа')
                                                ->body('Автомат үүсгэхээс өмнө орцуудыг гараар үүсгэх шаардлагатай.')
                                                ->danger()
                                                ->send();
                                            return;
                                        }

                                        // Validate form data before proceeding
                                        if (!($data[Korpus::AUTO_GENERATE] ?? false)) {
                                            Notification::make()
                                                ->title('Алдаа гарлаа')
                                                ->body('Автомат үүсгэх сонголтыг идэвхжүүлнэ үү.')
                                                ->danger()
                                                ->send();
                                            return;
                                        }

                                        if (!($data[Korpus::NUMBERING_TYPE] ?? null)) {
                                            Notification::make()
                                                ->title('Алдаа гарлаа')
                                                ->body('Дугаарлалтын төрлийг сонгоно уу.')
                                                ->danger()
                                                ->send();
                                            return;
                                        }

                                        if (!($data[Korpus::NUMBER_OF_FLOORS] ?? null) || $data[Korpus::NUMBER_OF_FLOORS] <= 0) {
                                            Notification::make()
                                                ->title('Алдаа гарлаа')
                                                ->body('Давхарын тоо 0-ээс их байх ёстой.')
                                                ->danger()
                                                ->send();
                                            return;
                                        }

                                        if (!($data[Korpus::DOORS_PER_FLOOR] ?? null) || $data[Korpus::DOORS_PER_FLOOR] <= 0) {
                                            Notification::make()
                                                ->title('Алдаа гарлаа')
                                                ->body('Тоотын тоо 0-ээс их байх ёстой.')
                                                ->danger()
                                                ->send();
                                            return;
                                        }

                                        if ($data[Korpus::NUMBERING_TYPE] == 3 && (!($data[Korpus::DIGIT_MULTIPLIER] ?? null) || !in_array($data[Korpus::DIGIT_MULTIPLIER], [10, 100, 1000]))) {
                                            Notification::make()
                                                ->title('Алдаа гарлаа')
                                                ->body('Давхар бүрээр дугаарлахад цифрийн үржүүлэгч (10, 100, 1000) сонгох шаардлагатай.')
                                                ->danger()
                                                ->send();
                                            return;
                                        }

                                        $doorNumberingService = app(DoorNumberingService::class);

                                        // Update the record with form data first
                                        $record->update([
                                            Korpus::AUTO_GENERATE => $data[Korpus::AUTO_GENERATE],
                                            Korpus::NUMBERING_TYPE => $data[Korpus::NUMBERING_TYPE],
                                            Korpus::NUMBER_OF_FLOORS => $data[Korpus::NUMBER_OF_FLOORS],
                                            Korpus::DOORS_PER_FLOOR => $data[Korpus::DOORS_PER_FLOOR],
                                            Korpus::DIGIT_MULTIPLIER => $data[Korpus::DIGIT_MULTIPLIER] ?? null,
                                        ]);

                                        $doorNumberingService->generateKorpusHierarchy($record);

                                        $totalDoors = $record->orcs->count() * $record->number_of_floors * $record->doors_per_floor;
                                        Notification::make()
                                            ->title('Амжилттай үүсгэлээ')
                                            ->body("Блок {$record->name}-д {$record->orcs->count()} орц, орц тутамд {$record->number_of_floors} давхар, давхар тутамд {$record->doors_per_floor} тоот. Нийт {$totalDoors} тоот үүсгэлээ.")
                                            ->success()
                                            ->send();
                                    } catch (\Exception $e) {
                                        Notification::make()
                                            ->title('Алдаа гарлаа')
                                            ->body($e->getMessage())
                                            ->danger()
                                            ->send();
                                    }
                                }),
                        ])
                    ])
                    ->columnSpan(['lg' => 3])
                    ->collapsible()
                    ->collapsed(),
            ])
            ->columns(3);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->heading('Блокууд')
            ->columns([
                Tables\Columns\TextColumn::make('')->label('№')->rowIndex()->sortable(),
                Tables\Columns\TextColumn::make(Korpus::NAME)->label('Нэр')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('bair.name')->label('Байр')->sortable()->searchable(),

                Tables\Columns\TextColumn::make(Korpus::CODE)->label('CV Security код')->sortable(),
                Tables\Columns\TextColumn::make('orcs_count')->counts('orcs')->label('Орцны тоо'),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->emptyStateActions([
                Tables\Actions\CreateAction::make(),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            RelationManagers\OrcsRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListKorpuses::route('/'),
            'create' => Pages\CreateKorpus::route('/create'),
            'edit' => Pages\EditKorpus::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        $service = resolve(UserInfoService::class);
        $sukh = $service->getAUSukh();
        return parent::getEloquentQuery()->whereHas('bair.sukh', function (Builder $query) use($sukh) {
            $query->where('id', $sukh->id);
        });
    }
}
