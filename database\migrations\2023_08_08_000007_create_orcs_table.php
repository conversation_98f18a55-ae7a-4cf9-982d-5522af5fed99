<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('orcs', function (Blueprint $table) {
            $table->id();
            $table->foreignId('korpus_id')->constrained('korpuses')->cascadeOnDelete();
            $table->string('number');
            $table->string('code', 30)->nullable(); // CVSecurity integration code

            // Auto-generation fields
            $table->boolean('auto_generate')->default(false);
            $table->integer('numbering_type')->nullable(); // 2 = Orc-wise, 3 = Floor-wise
            $table->integer('number_of_floors')->nullable();
            $table->integer('doors_per_floor')->nullable();
            $table->integer('digit_multiplier')->nullable(); // For Type 3 numbering

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('orcs');
    }
};
