<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('toots', function (Blueprint $table) {
            $table->foreignId('orc_id')->nullable()->after('davhar_id')->constrained('orcs')->cascadeOnDelete();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('toots', function (Blueprint $table) {
            $table->dropForeign(['orc_id']);
            $table->dropColumn('orc_id');
        });
    }
};
