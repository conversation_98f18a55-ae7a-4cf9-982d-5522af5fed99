<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add auto-generation fields to orcs table
        Schema::table('orcs', function (Blueprint $table) {
            $table->boolean('auto_generate')->default(false)->after('code');
            $table->integer('numbering_type')->nullable()->after('auto_generate'); // 2 = Orc-wise, 3 = Floor-wise
            $table->integer('number_of_floors')->nullable()->after('numbering_type');
            $table->integer('doors_per_floor')->nullable()->after('number_of_floors');
            $table->integer('digit_multiplier')->nullable()->after('doors_per_floor'); // For Type 3 numbering
        });

        // Add auto-generation fields to davhars table
        Schema::table('davhars', function (Blueprint $table) {
            $table->boolean('auto_generate')->default(false)->after('code');
            $table->integer('numbering_type')->nullable()->after('auto_generate'); // 2 = Orc-wise, 3 = Floor-wise
            $table->integer('doors_per_floor')->nullable()->after('numbering_type');
            $table->integer('digit_multiplier')->nullable()->after('doors_per_floor'); // For Type 3 numbering
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('orcs', function (Blueprint $table) {
            $table->dropColumn([
                'auto_generate',
                'numbering_type',
                'number_of_floors',
                'doors_per_floor',
                'digit_multiplier'
            ]);
        });

        Schema::table('davhars', function (Blueprint $table) {
            $table->dropColumn([
                'auto_generate',
                'numbering_type',
                'doors_per_floor',
                'digit_multiplier'
            ]);
        });
    }
};
