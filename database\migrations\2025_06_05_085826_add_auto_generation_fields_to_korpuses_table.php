<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('korpuses', function (Blueprint $table) {
            $table->boolean('auto_generate')->default(false)->after('code');
            $table->integer('numbering_type')->nullable()->after('auto_generate'); // 1 = Korpus-wise, 2 = Orc-wise, 3 = Floor-wise
            $table->integer('number_of_floors')->nullable()->after('numbering_type');
            $table->integer('doors_per_floor')->nullable()->after('number_of_floors');
            $table->integer('digit_multiplier')->nullable()->after('doors_per_floor'); // For Type 3 numbering
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('korpuses', function (Blueprint $table) {
            $table->dropColumn([
                'auto_generate',
                'numbering_type',
                'number_of_floors',
                'doors_per_floor',
                'digit_multiplier'
            ]);
        });
    }
};
