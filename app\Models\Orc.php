<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * App\Models\Orc
 *
 * @mixin IdeHelperOrc
 * @property int $id
 * @property string $number
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at

 * @property int $korpus_id
 * @property-read \App\Models\Korpus|null $korpus
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Toot> $toots
 * @property-read int|null $toots_count
 * @method static \Illuminate\Database\Eloquent\Builder|Orc newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Orc newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Orc query()
 * @method static \Illuminate\Database\Eloquent\Builder|Orc whereBeginTootNumber($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Orc whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Orc whereEndTootNumber($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Orc whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Orc whereKorpusId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Orc whereNumber($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Orc whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class Orc extends Model
{
    use HasFactory;

    const ID                  = 'id';
    const KORPUS_ID           = 'korpus_id';
    const NUMBER              = 'number';
    const CODE                = 'code';

    // Auto-generation constants
    const AUTO_GENERATE       = 'auto_generate';
    const NUMBERING_TYPE      = 'numbering_type';
    const NUMBER_OF_FLOORS    = 'number_of_floors';
    const DOORS_PER_FLOOR     = 'doors_per_floor';
    const DIGIT_MULTIPLIER    = 'digit_multiplier';

    const RELATION_KORPUS  = 'korpus';
    const RELATION_TOOTS   = 'toots';
    const RELATION_DAVHARS = 'davhars';

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $fillable = [
        self::ID,
        self::KORPUS_ID,
        self::NUMBER,
        self::CODE,
        self::AUTO_GENERATE,
        self::NUMBERING_TYPE,
        self::NUMBER_OF_FLOORS,
        self::DOORS_PER_FLOOR,
        self::DIGIT_MULTIPLIER
    ];

    public function korpus(): BelongsTo
    {
        return $this->belongsTo(Korpus::class);
    }

    public function toots()
    {
        return $this->hasMany(Toot::class);
    }

    public function davhars()
    {
        return $this->hasMany(Davhar::class)->orderBy('order', 'asc');
    }

    /**
     * Get total doors for this Orc based on auto-generation settings
     */
    public function getTotalDoorsAttribute(): int
    {
        if ($this->doors_per_floor && $this->number_of_floors) {
            return $this->number_of_floors * $this->doors_per_floor;
        }

        return $this->davhars->sum(function ($davhar) {
            return $davhar->toots->count();
        });
    }
}
