<?php

namespace App\Filament\Resources\Admin;

use App\Filament\Resources\Admin\DavharResource\Pages;
use App\Filament\Resources\Admin\DavharResource\RelationManagers;
use App\Models\Davhar;
use App\Services\UserInfoService;
use App\Services\DoorNumberingService;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Forms\Get;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Notifications\Notification;
use Illuminate\Database\Eloquent\Builder;


class DavharResource extends Resource
{
    protected static ?string $model = Davhar::class;

    protected static ?string $navigationIcon = 'heroicon-o-building-storefront';
    protected static ?string $navigationLabel = 'Давхарууд';
    protected static ?string $pluralModelLabel = 'Давхарууд';
    protected static ?string $modelLabel = 'давхар';
    protected static ?string $slug = 'davhars';

    // Hide from navigation - only accessible through Orc hierarchy
    protected static bool $shouldRegisterNavigation = false;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make()
                    ->schema([
                        Forms\Components\Placeholder::make('orc_info')
                            ->label('Орц')
                            ->content(function (?Davhar $record) {
                                if ($record && $record->orc) {
                                    return "{$record->orc->korpus->bair->name} - {$record->orc->korpus->name} - Орц {$record->orc->number}";
                                }

                                $orcId = request()->get('orc_id');
                                if ($orcId) {
                                    $orc = \App\Models\Orc::with('korpus.bair')->find($orcId);
                                    return $orc ? "{$orc->korpus->bair->name} - {$orc->korpus->name} - Орц {$orc->number}" : 'Тодорхойгүй';
                                }

                                return 'Тодорхойгүй';
                            }),

                        Forms\Components\Hidden::make(Davhar::ORC_ID)
                            ->default(function () {
                                return request()->get('orc_id');
                            }),

                        Forms\Components\TextInput::make(Davhar::NUMBER)
                            ->label('Давхарын дугаар')
                            ->required(),
                    ])
                    ->columns(2),

                // Auto-generation section
                Forms\Components\Section::make('Автомат тоот үүсгэх')
                    ->schema([
                        Forms\Components\Toggle::make(Davhar::AUTO_GENERATE)
                            ->label('Автомат тоот үүсгэх')
                            ->reactive()
                            ->afterStateUpdated(function ($state, callable $set) {
                                if (!$state) {
                                    // Clear auto-generation fields when disabled
                                    $set(Davhar::NUMBERING_TYPE, null);
                                    $set(Davhar::DOORS_PER_FLOOR, null);
                                    $set(Davhar::DIGIT_MULTIPLIER, null);
                                }
                            }),

                        Forms\Components\Select::make(Davhar::NUMBERING_TYPE)
                            ->label('Дугаарлалтын төрөл')
                            ->options([
                                2 => 'Орц бүрээр (Type 2)',
                                3 => 'Давхар бүрээр (Type 3)',
                            ])
                            ->visible(fn (Get $get) => $get(Davhar::AUTO_GENERATE))
                            ->reactive()
                            ->required(fn (Get $get) => $get(Davhar::AUTO_GENERATE)),

                        Forms\Components\TextInput::make(Davhar::DOORS_PER_FLOOR)
                            ->label('Энэ давхарт байх тоотын тоо')
                            ->numeric()
                            ->minValue(1)
                            ->visible(fn (Get $get) => $get(Davhar::AUTO_GENERATE))
                            ->required(fn (Get $get) => $get(Davhar::AUTO_GENERATE)),

                        Forms\Components\Select::make(Davhar::DIGIT_MULTIPLIER)
                            ->label('Цифрийн үржүүлэгч')
                            ->options([
                                10 => '10 (2 оронтой: 11, 12, 13...)',
                                100 => '100 (3 оронтой: 101, 102, 103...)',
                                1000 => '1000 (4 оронтой: 1001, 1002, 1003...)',
                            ])
                            ->visible(fn (Get $get) => $get(Davhar::AUTO_GENERATE) && $get(Davhar::NUMBERING_TYPE) == 3)
                            ->required(fn (Get $get) => $get(Davhar::AUTO_GENERATE) && $get(Davhar::NUMBERING_TYPE) == 3),

                        Forms\Components\Actions::make([
                            Forms\Components\Actions\Action::make('generate')
                                ->label('Тоот үүсгэх')
                                ->color('success')
                                ->icon('heroicon-o-plus-circle')
                                ->visible(fn (Get $get) => $get(Davhar::AUTO_GENERATE))
                                ->requiresConfirmation()
                                ->modalHeading('Тоот үүсгэх')
                                ->modalDescription('Энэ үйлдэл нь одоо байгаа бүх тоотуудыг устгаад шинээр үүсгэнэ. Та итгэлтэй байна уу?')
                                ->modalSubmitActionLabel('Тийм, үүсгэх')
                                ->action(function (Davhar $record, array $data) {
                                    try {
                                        // Validate form data before proceeding
                                        if (!($data[Davhar::AUTO_GENERATE] ?? false)) {
                                            Notification::make()
                                                ->title('Алдаа гарлаа')
                                                ->body('Автомат үүсгэх сонголтыг идэвхжүүлнэ үү.')
                                                ->danger()
                                                ->send();
                                            return;
                                        }

                                        $doorNumberingService = app(DoorNumberingService::class);

                                        // Update the record with form data first
                                        $record->update([
                                            Davhar::AUTO_GENERATE => $data[Davhar::AUTO_GENERATE],
                                            Davhar::NUMBERING_TYPE => $data[Davhar::NUMBERING_TYPE],
                                            Davhar::DOORS_PER_FLOOR => $data[Davhar::DOORS_PER_FLOOR],
                                            Davhar::DIGIT_MULTIPLIER => $data[Davhar::DIGIT_MULTIPLIER] ?? null,
                                        ]);

                                        $doorNumberingService->generateDavharToots($record);

                                        Notification::make()
                                            ->title('Амжилттай үүсгэлээ')
                                            ->body("Давхар {$record->number}-д {$record->doors_per_floor} тоот үүсгэлээ.")
                                            ->success()
                                            ->send();
                                    } catch (\Exception $e) {
                                        Notification::make()
                                            ->title('Алдаа гарлаа')
                                            ->body($e->getMessage())
                                            ->danger()
                                            ->send();
                                    }
                                }),
                        ])
                    ])
                    ->collapsible()
                    ->collapsed(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->heading('Давхарууд')
            ->columns([
                Tables\Columns\TextColumn::make('')->label('№')->rowIndex()->sortable(),
                Tables\Columns\TextColumn::make(Davhar::NUMBER)->label('Давхарын дугаар')->sortable()->searchable(),

                Tables\Columns\TextColumn::make('orc.number')->label('Орц')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('orc.korpus.name')->label('Блок')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('orc.korpus.bair.name')->label('Байр')->sortable()->searchable(),
                Tables\Columns\TextColumn::make(Davhar::CODE)->label('CV Security код')->sortable(),
                Tables\Columns\TextColumn::make('toots_count')->counts('toots')->label('Тоотын тоо'),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->emptyStateActions([
                Tables\Actions\CreateAction::make(),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            RelationManagers\TootsRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListDavhars::route('/'),
            'create' => Pages\CreateDavhar::route('/create'),
            'edit' => Pages\EditDavhar::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        $service = resolve(UserInfoService::class);
        $sukh = $service->getAUSukh();
        return parent::getEloquentQuery()->whereHas('orc.korpus.bair.sukh', function (Builder $query) use($sukh) {
            $query->where('id', $sukh->id);
        });
    }
}
